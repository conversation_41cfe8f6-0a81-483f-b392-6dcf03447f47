[name]
VixDiskLib_InitEx

[description]
[code]
VixError
VixDiskLib_InitEx(uint32 majorVersion,
                  uint32 minorVersion,
                  VixDiskLibGenericLogFunc *log,
                  VixDiskLibGenericLogFunc *warn,
                  VixDiskLibGenericLogFunc *panic,
                  const char *libDir,
                  const char *configFilePath);
[endcode]

This function initializes VixDiskLib.

[parameters]
   majorVersion - Major version number of VixDiskLib (VDDK release number).
   minorVersion - Minor version number of VixDiskLib (VDDK dot release number).
   log - User defined function to write log messages.
   warn - User defined function to write warning messages.
          In addition, VixDiskLib also writes this message to stderr.
   panic - User defined function to write panic message.
   libDir - A directory path to locate dependent DLL / shared objects
            (e.g vixDiskLibVim.dll, libeay32.dll). Can be NULL.
   configFilePath - Location of the config file in local encoding. Can be NULL.

[return]
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.
 
[remarks]
* VixDiskLib_InitEx() must be the first VixDiskLib function to be called.
* Due to internationalization, you may not call VixDiskLib_InitEx() more than once per process.
* In a multi-threaded program, it is the application programmer's responsibility
  to serialize writing to the log file.
* VixDiskLib_InitEx() must be matched with VixDiskLib_Exit() near end of program.
* Calling VixDiskLib_InitEx() with NULL parameters for log, warn, and panic results in
  VixDiskLib creating and writing to a default log file.
* Passing NULL as configFilePath causes VixDiskLib to choose default configuration parameters.
* For a list of supported name/value pairs in the configFile,
  see the Virtual Disk Programming Guide.

[example]
[code]
   vixError = VixDiskLib_InitEx(VIXDISKLIB_VERSION_MAJOR,
                                VIXDISKLIB_VERSION_MINOR,
                                LogFunction, WarningFunction, PanicFunction,
                                "C:\\Program Files\\Super Backup Company\\Super Backup 1.0",
                                "C:\\Documents And Settings\\All Users\\Application Data\\Super Backup 1.0\\vixdisklib.ini");
[endcode]
