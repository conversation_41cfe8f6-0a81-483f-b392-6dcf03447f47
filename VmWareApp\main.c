#include "VmWareApp.h"
#include "KernelModeInject.h"
#include "debug.h"
#include "pe.h"
#include "VmWareDisk.h"
#include <tlhelp32.h>

DWORD WINAPI ThreadProc(LPVOID lpThreadParameter) {
	DWORD pid = *(DWORD*)lpThreadParameter;

	DebugProcess(pid);
	return 0;
}

DWORD FindVMwareProcess() {
	HANDLE hSnapshot;
	PROCESSENTRY32 pe32;
	DWORD vmwarePid = 0;

	// 创建进程快照
	hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hSnapshot == INVALID_HANDLE_VALUE) {
		printf("CreateToolhelp32Snapshot failed: %d\n", GetLastError());
		return 0;
	}

	pe32.dwSize = sizeof(PROCESSENTRY32);

	// 遍历进程
	if (Process32First(hSnapshot, &pe32)) {
		do {
			// 查找VMware相关进程
			if (_stricmp(pe32.szExeFile, "vmware.exe") == 0 ||
				_stricmp(pe32.szExeFile, "vmware-vmx.exe") == 0) {
				printf("Found VMware process: %s (PID: %d)\n", pe32.szExeFile, pe32.th32ProcessID);
				vmwarePid = pe32.th32ProcessID;
				break;
			}
		} while (Process32Next(hSnapshot, &pe32));
	}

	CloseHandle(hSnapshot);
	return vmwarePid;
}

int main() {

	DWORD pid = 0; // 需要替换为实际的VMware进程PID
	if (!VmWareThroughInit(pid))
	{
		printf("main��VmWare Through Initialization Failed.\n");
		return -1;
	}
	//�������������ڴ棬����ڴ湦��
	//if (!VMFindVmProcessData("notepad.exe", VMGetVmwareDestProcData()))
	//{
	//	printf("main: Find Process Data Failed.\n");
	//	return -1;
	//}
	//DWORD64 buf_addr = 0;
	//for (DWORD64 i = 0; i < 0x7FFFFFFFFFFF; i+=0x1000) {
	//	VMReadVmVirtualAddr(&buf_addr, VMGetVmwareDestProcData()->DestProcessCr3, i, 8);
	//}

	if (!KMIKernelInject())
	{
		printf("main��Kernel injection failed.\n");
		return -1;
	}
	VMLoadDriver("\\??\\c:\\test\\DriverTest.sys");
	return 0;
}
