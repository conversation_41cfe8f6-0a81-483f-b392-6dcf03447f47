<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLib_Unlink</b>
<h1>Description</h1>
<pre>
VixError
VixDiskLib_Unlink(VixDiskLibConnection connection,
                  const char *path);
</pre>
<p>
Delete the virtual disk including all the extents.
<h1>Parameters</h1>
<dl>
<dt><i>connection</i></dt>
<dd>
A valid vixDiskLib connection to manipulate hosted virtual
   disks.
</dd>
<dt><i>path</i></dt>
<dd>
Path name for the virtual disk to be deleted.
</dd>
</dl>
<h1>Return Value</h1>
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.
<h1>Remarks</h1>
<ul>
<li> VixDiskLib_Unlink() can only delete hosted disks.
<li> If the path refers to a parent virtual disk, the child disk will be orphaned.
</ul>
<h1>Example</h1>
<pre>
   vixError = VixDiskLib_Unlink(appGlobals.connection,
                                appGlobals.diskPath);
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
