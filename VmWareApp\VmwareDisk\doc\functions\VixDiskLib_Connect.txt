[name]
VixDiskLib_Connect

[description]
[code]
VixError
VixDiskLib_Connect(const VixDiskLibConnectParams *connectParams,
                   VixDiskLibConnection *connection);
[endcode]

This function opens a connection to VixDiskLib. You can use this connection to 
later on request various services from VixDiskLib.

See also VixDiskLib_ConnectEx(), which has additional parameters and can open
more efficient connections to managed disk.

[parameters]
   connectParams - A struct containing the parameters to establish a connection.
   connection - A handle for the connection. This is an output parameter.

[return]
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.

[remarks]
* To create a connection that can open hosted type disks, use null values for
  vmname, hostname, username, password and port. This establishes a connection
  to the localhost.
* To create a connection to open a managed disk, provide valid credentials for
  an ESX server that can access the virtual disk.
* This function blocks until the connection completes.
* Every call to VixDiskLib_Connect() should have a matching 
  VixDiskLib_Disconnect().

[example]
[code]
   VixDiskLibConnectParams cnxParams = {0};
   if (appGlobals.isRemote) {
      cnxParams.vmName = NULL;
      cnxParams.serverName = hostName;
      cnxParams.credType = VIXDISKLIB_CRED_UID;
      cnxParams.creds.uid.userName = userName;
      cnxParams.creds.uid.password = password;
      cnxParams.port = port;
   }
   VixError vixError = VixDiskLib_Connect(&cnxParams,
                                          &connection);
[endcode]

