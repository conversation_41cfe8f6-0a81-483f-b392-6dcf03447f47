<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLib_Clone</b>
<h1>Description</h1>
<pre>
VixError
VixDiskLib_Clone(const VixDiskLibConnection dstConnection,
                 const char *dstPath,
                 const VixDiskLibConnection srcConnection,
                 const char *srcPath,
                 const VixDiskLibCreateParams *vixCreateParams,
                 VixDiskLibProgressFunc progressFunc,
                 void *progressCallbackData,
                 Bool  overWrite);
</pre>
<p>
This function synchronously copies a disk to the destination converting
 formats as appropriate.
<h1>Parameters</h1>
<dl>
<dt><i>dstConnection</i></dt>
<dd>
A VixDiskLib connection that can access the destination disk.
</dd>
<dt><i>dstPath</i></dt>
<dd>
Absolute path for the (new) destination disk.
</dd>
<dt><i>srcConnection</i></dt>
<dd>
A Valid connection that can access the source disk.
</dd>
<dt><i>srcPath</i></dt>
<dd>
Absolute path for the source disk.
</dd>
<dt><i>createParams</i></dt>
<dd>
CreationParameters (disktype, hardware type...) for the newly 
   created disk. When the destination is remote, createParams is currently 
   ignored and disk with appropriate size and adapter type is created.
</dd>
<dt><i>progressFunc</i></dt>
<dd>
A pointer to a function of type VixDiskLibProgressFunc.
   VixDiskLib will call this function periodically to update progress.
</dd>
<dt><i>progressCallbackData</i></dt>
<dd>
Opaque data that VixDiskLib_Clone() will pass while 
   calling progressFunc.
</dd>
<dt><i>overWrite</i></dt>
<dd>
If TRUE, VixDiskLib_Clone() will continue even when the destination
   file exists.
</dd>
</dl>
<h1>Return Value</h1>
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.
<h1>Remarks</h1>
<ul>
<li> It is not possible to directly create a managed disk using VixDiskLib. 
The recommended way to create a managed disk is create a hosted disk and
use VixDiskLib_Clone() function to copy the disk to a VMFS datastore.
</ul>
<h1>Example</h1>
<pre>
   vixError = VixDiskLib_Clone(appGlobals.connection,
                               appGlobals.diskPath,
                               srcConnection,
                               appGlobals.srcPath,
                               &createParams,
                               CloneProgressFunc,
                               NULL,   // clientData
                               TRUE);  // doOverWrite
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
