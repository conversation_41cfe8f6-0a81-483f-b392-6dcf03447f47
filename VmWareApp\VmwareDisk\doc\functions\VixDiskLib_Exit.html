<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLib_Exit</b>
<h1>Description</h1>
<pre>
void
VixDiskLib_Exit(void);
</pre>
<p>
This function releases all resources held by VixDiskLib.
<h1>Return Value</h1>
None.
<h1>Remarks</h1>
<ul>
<li> Every VixDiskLib_Init() must have a matching call to VixDiskLib_Exit().
</ul>
<h1>Example</h1>
<pre>
   VixDiskLib_Exit();
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
