<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLib_Shrink</b>
<h1>Description</h1>
<pre>
VixError
VixDiskLib_Shrink(VixDiskLibHandle diskHandle,
                  VixDiskLibProgressFunc progressFunc,
                  void *progressCallbackData);
</pre>
<p>
This function reclaims blocks of zeroes from the virtual disk.
<h1>Parameters</h1>
<dl>
<dt><i>diskHandle</i></dt>
<dd>
Handle to an open virtual disk.
</dd>
<dt><i>progressFunc</i></dt>
<dd>
A pointer to a function of type VixDiskLibProgressFunc. 
   VixDiskLib will call this function periodically to update progress.
</dd>
<dt><i>progressCallbackData</i></dt>
<dd>
Opaque data that VixDiskLib will pass while calling 
   progressFunc.
</dd>
</dl>
<h1>Return Value</h1>
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.
<h1>Remarks</h1>
<ul>
<li> VixDiskLib_Shrink() does not change the virtual disk capacity.
<li> VixDiskLib_Shrink() can only shrink hosted disks.
<li> VixDiskLib_Shrink() is only applicable for sparse disks.
</ul>
<h1>Example</h1>
<pre>
   vixError = VixDiskLib_Shrink(disk.Handle(),
                                ShrinkProgressFunc,
                                NULL);
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
