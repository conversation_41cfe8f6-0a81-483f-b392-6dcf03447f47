[name]
VixDiskLib_Defragment

[description]
[code]
VixError
VixDiskLib_Defragment(VixDiskLibHandle handle,
                      VixDiskLibProgressFunc progressFunc,
                      void *progressCallbackData);
[endcode]

This function defragments an existing virtual disk.

[parameters]
   disk<PERSON>and<PERSON> - Handle to an open virtual disk.
   progressFunc - A pointer to a function of type VixDiskLibProgressFunc.
   VixDiskLib will call this function periodically to update progress.
   progressCallbackData - Opaque data that VixDiskLib will pass while calling 
   progressFunc.

[return]
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.

[remarks]
* VixDiskLib_Defragment() can only defragment hosted disks.
* VixDiskLib_Defragment() is only applicable for sparse disks.

[example]
[code]
   vixError = VixDiskLib_Defragment(disk.Handle(),
                                    DefragProgressFunc,
                                    NULL);
[endcode]
