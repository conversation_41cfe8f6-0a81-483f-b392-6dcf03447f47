[name]
VixDiskLib_Shrink

[description]
[code]
VixError
VixDiskLib_Shrink(VixDiskLib<PERSON>andle diskHandle,
                  VixDiskLibProgressFunc progressFunc,
                  void *progressCallbackData);
[endcode]

This function reclaims blocks of zeroes from the virtual disk.

[parameters]
   disk<PERSON>and<PERSON> - Handle to an open virtual disk.
   progressFunc - A pointer to a function of type VixDiskLibProgressFunc. 
   VixDiskLib will call this function periodically to update progress.
   progressCallbackData - Opaque data that VixDiskLib will pass while calling 
   progressFunc.

[return]
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.

[remarks]
* VixDiskLib_Shrink() does not change the virtual disk capacity.
* VixDiskLib_Shrink() can only shrink hosted disks.
* VixDiskLib_Shrink() is only applicable for sparse disks.

[example]
[code]
   vixError = VixDiskLib_Shrink(disk.Handle(),
                                ShrinkProgressFunc,
                                NULL);
[endcode]

