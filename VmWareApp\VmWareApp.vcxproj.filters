﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="MemX64.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="misc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pe.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="VmWareApp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="WindowsNt.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ShellCode.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="KernelModeInject.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="debug.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Win10X64Stage3.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="VmWareDisk.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="vad.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="KernelModeInject.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="MemX64.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="misc.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="pe.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="VmWareApp.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="debug.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Win10X64Stage3.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="VmWareDisk.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="vad.c">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Win10X64Inject.asm">
      <Filter>源文件</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>