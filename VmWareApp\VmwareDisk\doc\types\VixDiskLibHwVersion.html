<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLibHwVersion</b>
<h1>Description</h1>
Type of virtual hardware version.
<p>
The properties of a virtual machine include a virtual hardware version
that encapsulates the capabilities of a virtual machine.
VMware products generally support the current and the previous virtual hardware versions.
You should upgrade virtual machines older than the previous virtual hardware version
so they continue to run.
<h1>Values</h1>
<dl>
<dt><i>VIXDISKLIB_HWVERSION_WORKSTATION_4</i></dt>
<dd>
(3) VMware Workstation 4.x and GSX Server 3.x
</dd>
<dt><i>VIXDISKLIB_HWVERSION_WORKSTATION_5</i></dt>
<dd>
(4) VMware Workstation 5.x and VMware Server 1.0.x
</dd>
<dt><i>VIXDISKLIB_HWVERSION_ESX30</i></dt>
<dd>
(4) VMware ESX 3.0 and 3.5, same type as above
</dd>
<dt><i>VIXDISKLIB_HWVERSION_WORKSTATION_6</i></dt>
<dd>
(6) VMware Workstation 6.0.x and VMware Server 2.0
</dd>
</dl>
<h1>Remarks</h1>
<ul>
<li> VMware Workstation 6.5 and ESX/ESXi 4.x (and later) use virtual hardware version 7
for hot-plug devices.
<li> Currently the default is VIXDISKLIB_HWVERSION_WORKSTATION_6,
although this could change.
<li> Virtual hardware version 5 was never public.
</ul>
<h1>Example</h1>
<pre>
   VixDiskLibCreateParams createParams;

   createParams.adapterType = VIXDISKLIB_ADAPTER_SCSI_LSILOGIC;
   createParams.capacity = 204800;
   createParams.diskType = VIXDISKLIB_DISK_MONOLITHIC_SPARSE;
   createParams.hwVersion = VIXDISKLIB_HWVERSION_WORKSTATION_6;
   VixError vixError = VixDiskLib_Create(appGlobals.connection,
                                appGlobals.diskPath,
                                &createParams,
                                NULL,
                                NULL);
   CHECK_AND_THROW(vixError);
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
