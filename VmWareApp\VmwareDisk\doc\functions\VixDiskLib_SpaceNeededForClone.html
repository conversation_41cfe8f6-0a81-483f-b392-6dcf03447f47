<html>
<head>
<meta HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=ISO-8859-1">
<meta HTTP-EQUIV="Content-Style-Type" CONTENT="text/css">
<link rel="stylesheet" href="../foundrydoc.css" type="text/css" charset="ISO-8859-1">
</head>
<body>
<h1>Name</h1>
<b>VixDiskLib_SpaceNeededForClone</b>
<h1>Description</h1>
<pre>
VixError
VixDiskLib_SpaceNeededForClone(VixDiskLibHandle srcHandle,
                               VixDiskLibDiskType diskType,
                               uint64 *spaceNeeded);
</pre>
<p>
This function computes the space required in bytes to clone a virtual disk.
<h1>Parameters</h1>
<dl>
<dt><i>srcHandle</i></dt>
<dd>
Handle to the disk to be copied.
</dd>
<dt><i>diskType</i></dt>
<dd>
The type of the to be created disk. If diskType is set to 
     VIXDISKLIB_DISK_UNKNOWN, the source disk type is assumed.
</dd>
<dt><i>spaceNeeded</i></dt>
<dd>
Space needed in bytes for the new disk.
</dd>
</dl>
<h1>Return Value</h1>
VIX_OK if the function succeeded, otherwise an appropriate VIX error code.
<h1>Remarks</h1>
<ul>
<li> Calculations take into account possible format conversion.
</ul>
<h1>Example</h1>
<pre>
   vixError = VixDiskLib_SpaceNeededForClone(child.Handle(),
                                             VIXDISKLIB_DISK_VMFS_FLAT,
                                             &spaceReq);
</pre>
</body>
</html>
<hr>Copyright (C) 2007-2017 VMware, Inc.  All rights reserved.
